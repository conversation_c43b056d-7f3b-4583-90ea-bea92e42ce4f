version = 3.9.4

style = default

maxColumn = 100

// Docstring wrapping breaks doctests
docstrings.wrap = false

// Vertical alignment is pretty, but leads to bigger diffs
align.preset = none

danglingParentheses.preset = true

rewrite.rules = [
  AvoidInfix
  RedundantBraces
  RedundantParens
  PreferCurlyFors
  SortModifiers
]

rewrite.sortModifiers.order = [
  override, implicit, private, protected, final, sealed, abstract, lazy
]

rewrite.trailingCommas.style = multiple

project.excludeFilters = [
   "scalafix/*",
   "scalafix-internal/input/*",
   "scalafix-internal/output/*"
]

runner.dialect = scala212

fileOverride {
  "glob:**/scala-3/**/*.scala" {
    runner.dialect = scala3
  }
  "glob:**/scala-2.13/**/*.scala" {
    runner.dialect = scala213
  }
}
/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s
package syntax

import org.typelevel.ci.CIString

trait StringSyntax {
  @deprecated(
    "Use org.typelevel.ci.CIString.apply instead, " +
      """or for literals import org.typelevel.ci._ and replace "foo".ci with ci"foo"""",
    "0.22.0",
  )
  implicit def http4sStringSyntax(s: String): StringOps =
    new StringOps(s)
}

@deprecated(
  "Use org.typelevel.ci.CIString.apply instead, " +
    """or for literals import org.typelevel.ci._ and replace "foo".ci with ci"foo"""",
  "0.22.0",
)
final class StringOps(val self: String) extends AnyVal {
  def ci: CIString = CIString(self)
}

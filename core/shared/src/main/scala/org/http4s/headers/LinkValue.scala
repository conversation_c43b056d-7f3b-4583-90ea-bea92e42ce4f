/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s
package headers

import org.http4s.util.Renderable
import org.http4s.util.Writer

final case class LinkValue(
    uri: Uri,
    rel: Option[String] = None,
    rev: Option[String] = None,
    title: Option[String] = None,
    `type`: Option[MediaRange] = None,
) extends Renderable {
  override def render(writer: Writer): writer.type = {
    writer << "<" << uri.toString << ">"
    rel.foreach(writer.append("; rel=").append(_))
    rev.foreach(writer.append("; rev=").append(_))
    title.foreach(writer.append("; title=").append(_))
    `type`.foreach { m =>
      writer.append("; type=")
      HttpCodec[MediaRange].render(writer, m)
    }
    writer
  }
}

/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s.headers

import cats.parse.Parser
import org.http4s._
import org.typelevel.ci._

final case class `X-Forwarded-Proto`(scheme: Uri.Scheme) extends AnyVal

object `X-Forwarded-Proto` {
  private[http4s] val parser: Parser[`X-Forwarded-Proto`] = Uri.Parser.scheme.map(apply)

  def parse(s: String): ParseResult[`X-Forwarded-Proto`] =
    ParseResult.fromParser(parser, "Invalid X-Forwarded-Proto header")(s)

  implicit val headerInstance: Header[`X-Forwarded-Proto`, Header.Single] =
    Header.createRendered(
      ci"X-Forwarded-Proto",
      _.scheme,
      parse,
    )
}

/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s

import cats._

final case class ContextResponse[F[_], A](context: A, response: Response[F]) {
  def mapContext[B](f: A => B): ContextResponse[F, B] =
    ContextResponse(f(context), response)

  def mapK[G[_]](fk: F ~> G): ContextResponse[G, A] =
    ContextResponse(context, response.mapK(fk))
}

// Included to avoid binary compatibility issues with the apply method if/when
// we ever need a companion object in the future.
object ContextResponse {}

/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s

import cats.syntax.all._
import org.http4s.util.Renderer

trait HttpCodec[A] extends Renderer[A] {
  def parse(s: String): ParseResult[A]

  /** Warning: partial method. Intended for tests and macros that have
    * assured that `s` can be parsed to a valid `A`.
    */
  final def parseOrThrow(s: String): A =
    parse(s).valueOr(throw _)
}

object HttpCodec {
  def apply[A](implicit A: HttpCodec[A]): HttpCodec[A] = A
}

package org.http4s.build

import sbt._
import sbt.Keys._

object CentralRequirementsPlugin extends AutoPlugin {
  override def trigger = allRequirements

  override lazy val projectSettings = Seq(
    developers ++= List(
      // n.b. alphabetical by GitHub username
      Dev<PERSON><PERSON>("aeons", "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", url("https://github.com/aeons")),
      <PERSON><PERSON><PERSON>("before", "<PERSON>", "", url("https://github.com/before")),
      <PERSON><PERSON><PERSON>("bfritz", "<PERSON>", "", url("https://github.com/bfritz")),
      <PERSON><PERSON><PERSON>(
        "bryce-anderson",
        "<PERSON>",
        "<EMAIL>",
        url("https://github.com/bryce-anderson"),
      ),
      <PERSON><PERSON><PERSON>(
        "casualjim",
        "<PERSON>",
        "<EMAIL>",
        url("https://github.com/casualjim"),
      ),
      <PERSON><PERSON><PERSON>("cencarnac<PERSON>", "<PERSON>", "", url("https://github.com/cencarnacion")),
      <PERSON><PERSON><PERSON>(
        "ChristopherDaven<PERSON>",
        "<PERSON>",
        "<EMAIL>",
        url("https://github.com/ChristopherDavenport"),
      ),
      Developer("cquiroz", "<PERSON> Quiroz", "", url("https://github.com/cquiroz")),
      Developer("hvesalai", "Heikki Vesalainen", "", url("https://github.com/hvesalai")),
      Developer("jcranky", "Paulo Siqueira", "", url("https://github.com/jcranky")),
      Developer("jedesah", "Jean-Rémi Desjardins", "", url("https://github.com/jedesah")),
      Developer("jmcardon", "Jose Cardona", "", url("https://github.com/jmcardon")),
      Developer(
        "julien-truffaut",
        "Julien Truffaut",
        "",
        url("https://github.com/julien-truffaut"),
      ),
      Developer("kryptt", "Rodolfo Hansen", "", url("https://github.com/kryptt")),
      Developer("reactormonk", "Simon Hafner", "", url("https://github.com/reactormonk")),
      Developer("refried", "Arya Irani", "", url("https://github.com/refried")),
      Developer(
        "rossabaker",
        "Ross A. Baker",
        "<EMAIL>",
        url("https://github.com/rossabaker"),
      ),
      Developer("shengc", "Sheng Chen", "", url("https://github.com/shengc")),
      Developer("SystemFw", "Fabio Labella", "", url("https://github.com/SystemFw")),
    ),
    licenses := Seq("Apache-2.0" -> url("https://www.apache.org/licenses/LICENSE-2.0.html")),
    homepage := Some(url("https://http4s.org/")),
    scmInfo := Some(
      ScmInfo(url("https://github.com/http4s/http4s"), "**************:http4s/http4s.git")
    ),
    Compile / packageBin / publishArtifact := true,
    Compile / packageSrc / publishArtifact := true,
    Test / publishArtifact := false,
    credentials ++= (for {
      username <- sys.env.get("SONATYPE_USERNAME")
      password <- sys.env.get("SONATYPE_PASSWORD")
    } yield Credentials(
      "Sonatype Nexus Repository Manager",
      "oss.sonatype.org",
      username,
      password,
    )).toSeq,
  )
}

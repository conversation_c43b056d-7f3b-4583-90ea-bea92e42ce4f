{"nodes": {"devshell": {"inputs": {"nixpkgs": ["typelevel-nix", "nixpkgs"]}, "locked": {"lastModified": 1735644329, "narHash": "sha256-tO3HrHriyLvipc4xr+Ewtdlo7wM1OjXNjlWRgmM7peY=", "owner": "numtide", "repo": "de<PERSON><PERSON><PERSON>", "rev": "f7795ede5b02664b57035b3b757876703e2c3eac", "type": "github"}, "original": {"owner": "numtide", "repo": "de<PERSON><PERSON><PERSON>", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1738422722, "narHash": "sha256-Q4vhtbLYWBUnjWD4iQb003Lt+N5PuURDad1BngGKdUs=", "owner": "nixos", "repo": "nixpkgs", "rev": "102a39bfee444533e6b4e8611d7e92aa39b7bec1", "type": "github"}, "original": {"owner": "nixos", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"flake-utils": ["typelevel-nix", "flake-utils"], "nixpkgs": ["typelevel-nix", "nixpkgs"], "typelevel-nix": "typelevel-nix"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "typelevel-nix": {"inputs": {"devshell": "de<PERSON><PERSON><PERSON>", "flake-utils": "flake-utils", "nixpkgs": "nixpkgs"}, "locked": {"lastModified": 1738606142, "narHash": "sha256-tqJ24RSupaL7ERmV6MXWLj4FNNhqWPhxch7hT7QfOGg=", "owner": "typelevel", "repo": "typelevel-nix", "rev": "16bad7be5d8e9e29fc587fc3ac1221a3b449e5b7", "type": "github"}, "original": {"owner": "typelevel", "repo": "typelevel-nix", "type": "github"}}}, "root": "root", "version": 7}
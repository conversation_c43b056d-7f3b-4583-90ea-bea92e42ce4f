/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s.internal

import org.http4s.Http4sSuite

class TlsSuite extends Http4sSuite {

  /* List of cipher suites obtained from: https://www.iana.org/assignments/tls-parameters/tls-parameters.txt.
   */
  private val cipherSuiteMap = Map[String, Int](
    "TLS_NULL_WITH_NULL_NULL" -> 0,
    "TLS_RSA_WITH_NULL_MD5" -> 0,
    "TLS_RSA_WITH_NULL_SHA" -> 0,
    "TLS_RSA_EXPORT_WITH_RC4_40_MD5" -> 40,
    "TLS_RSA_WITH_RC4_128_MD5" -> 128,
    "TLS_RSA_WITH_RC4_128_SHA" -> 128,
    "TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5" -> 40,
    "TLS_RSA_WITH_IDEA_CBC_SHA" -> 128,
    "TLS_RSA_EXPORT_WITH_DES40_CBC_SHA" -> 40,
    "TLS_RSA_WITH_DES_CBC_SHA" -> 56,
    "TLS_RSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_DH_DSS_EXPORT_WITH_DES40_CBC_SHA" -> 40,
    "TLS_DH_DSS_WITH_DES_CBC_SHA" -> 56,
    "TLS_DH_DSS_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_DH_RSA_EXPORT_WITH_DES40_CBC_SHA" -> 40,
    "TLS_DH_RSA_WITH_DES_CBC_SHA" -> 56,
    "TLS_DH_RSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA" -> 40,
    "TLS_DHE_DSS_WITH_DES_CBC_SHA" -> 56,
    "TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA" -> 40,
    "TLS_DHE_RSA_WITH_DES_CBC_SHA" -> 56,
    "TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_DH_anon_EXPORT_WITH_RC4_40_MD5" -> 40,
    "TLS_DH_anon_WITH_RC4_128_MD5" -> 128,
    "TLS_DH_anon_EXPORT_WITH_DES40_CBC_SHA" -> 40,
    "TLS_DH_anon_WITH_DES_CBC_SHA" -> 56,
    "TLS_DH_anon_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_KRB5_WITH_DES_CBC_SHA" -> 56,
    "TLS_KRB5_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_KRB5_WITH_RC4_128_SHA" -> 128,
    "TLS_KRB5_WITH_IDEA_CBC_SHA" -> 128,
    "TLS_KRB5_WITH_DES_CBC_MD5" -> 56,
    "TLS_KRB5_WITH_3DES_EDE_CBC_MD5" -> 168,
    "TLS_KRB5_WITH_RC4_128_MD5" -> 128,
    "TLS_KRB5_WITH_IDEA_CBC_MD5" -> 128,
    "TLS_KRB5_EXPORT_WITH_DES_CBC_40_SHA" -> 40,
    "TLS_KRB5_EXPORT_WITH_RC2_CBC_40_SHA" -> 40,
    "TLS_KRB5_EXPORT_WITH_RC4_40_SHA" -> 40,
    "TLS_KRB5_EXPORT_WITH_DES_CBC_40_MD5" -> 40,
    "TLS_KRB5_EXPORT_WITH_RC2_CBC_40_MD5" -> 40,
    "TLS_KRB5_EXPORT_WITH_RC4_40_MD5" -> 40,
    "TLS_PSK_WITH_NULL_SHA" -> 0,
    "TLS_DHE_PSK_WITH_NULL_SHA" -> 0,
    "TLS_RSA_PSK_WITH_NULL_SHA" -> 0,
    "TLS_RSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_DH_DSS_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_DH_RSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_DHE_DSS_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_DHE_RSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_DH_anon_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_RSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_DH_DSS_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_DH_RSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_DHE_DSS_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_DHE_RSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_DH_anon_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_RSA_WITH_NULL_SHA256" -> 0,
    "TLS_RSA_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_RSA_WITH_AES_256_CBC_SHA256" -> 256,
    "TLS_DH_DSS_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_DH_RSA_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_DHE_DSS_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_RSA_WITH_CAMELLIA_128_CBC_SHA" -> 128,
    "TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA" -> 128,
    "TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA" -> 128,
    "TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA" -> 128,
    "TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA" -> 128,
    "TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA" -> 128,
    "TLS_DHE_RSA_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_DH_DSS_WITH_AES_256_CBC_SHA256" -> 256,
    "TLS_DH_RSA_WITH_AES_256_CBC_SHA256" -> 256,
    "TLS_DHE_DSS_WITH_AES_256_CBC_SHA256" -> 256,
    "TLS_DHE_RSA_WITH_AES_256_CBC_SHA256" -> 256,
    "TLS_DH_anon_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_DH_anon_WITH_AES_256_CBC_SHA256" -> 256,
    "TLS_RSA_WITH_CAMELLIA_256_CBC_SHA" -> 256,
    "TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA" -> 256,
    "TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA" -> 256,
    "TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA" -> 256,
    "TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA" -> 256,
    "TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA" -> 256,
    "TLS_PSK_WITH_RC4_128_SHA" -> 128,
    "TLS_PSK_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_PSK_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_PSK_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_DHE_PSK_WITH_RC4_128_SHA" -> 128,
    "TLS_DHE_PSK_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_DHE_PSK_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_DHE_PSK_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_RSA_PSK_WITH_RC4_128_SHA" -> 128,
    "TLS_RSA_PSK_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_RSA_PSK_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_RSA_PSK_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_RSA_WITH_SEED_CBC_SHA" -> 128,
    "TLS_DH_DSS_WITH_SEED_CBC_SHA" -> 128,
    "TLS_DH_RSA_WITH_SEED_CBC_SHA" -> 128,
    "TLS_DHE_DSS_WITH_SEED_CBC_SHA" -> 128,
    "TLS_DHE_RSA_WITH_SEED_CBC_SHA" -> 128,
    "TLS_DH_anon_WITH_SEED_CBC_SHA" -> 128,
    "TLS_RSA_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_RSA_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_DH_RSA_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_DH_RSA_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_DHE_DSS_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_DHE_DSS_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_DH_DSS_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_DH_DSS_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_DH_anon_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_DH_anon_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_PSK_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_PSK_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_DHE_PSK_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_DHE_PSK_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_RSA_PSK_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_RSA_PSK_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_PSK_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_PSK_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_PSK_WITH_NULL_SHA256" -> 0,
    "TLS_PSK_WITH_NULL_SHA384" -> 0,
    "TLS_DHE_PSK_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_DHE_PSK_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_DHE_PSK_WITH_NULL_SHA256" -> 0,
    "TLS_DHE_PSK_WITH_NULL_SHA384" -> 0,
    "TLS_RSA_PSK_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_RSA_PSK_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_RSA_PSK_WITH_NULL_SHA256" -> 0,
    "TLS_RSA_PSK_WITH_NULL_SHA384" -> 0,
    "TLS_RSA_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_RSA_WITH_CAMELLIA_256_CBC_SHA256" -> 256,
    "TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA256" -> 256,
    "TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA256" -> 256,
    "TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA256" -> 256,
    "TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256" -> 256,
    "TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA256" -> 256,
    "TLS_SM4_GCM_SM3" -> 128,
    "TLS_SM4_CCM_SM3" -> 128,
    "TLS_AES_128_GCM_SHA256" -> 128,
    "TLS_AES_256_GCM_SHA384" -> 256,
    "TLS_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_AES_128_CCM_SHA256" -> 128,
    "TLS_AES_128_CCM_8_SHA256" -> 128,
    "TLS_ECDH_ECDSA_WITH_NULL_SHA" -> 0,
    "TLS_ECDH_ECDSA_WITH_RC4_128_SHA" -> 128,
    "TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_ECDHE_ECDSA_WITH_NULL_SHA" -> 0,
    "TLS_ECDHE_ECDSA_WITH_RC4_128_SHA" -> 128,
    "TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_ECDH_RSA_WITH_NULL_SHA" -> 0,
    "TLS_ECDH_RSA_WITH_RC4_128_SHA" -> 128,
    "TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_ECDHE_RSA_WITH_NULL_SHA" -> 0,
    "TLS_ECDHE_RSA_WITH_RC4_128_SHA" -> 128,
    "TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_ECDH_anon_WITH_NULL_SHA" -> 0,
    "TLS_ECDH_anon_WITH_RC4_128_SHA" -> 128,
    "TLS_ECDH_anon_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_ECDH_anon_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_ECDH_anon_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_SRP_SHA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_SRP_SHA_RSA_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_SRP_SHA_DSS_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_SRP_SHA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_SRP_SHA_RSA_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_SRP_SHA_DSS_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_SRP_SHA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_SRP_SHA_RSA_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_SRP_SHA_DSS_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_PSK_WITH_RC4_128_SHA" -> 128,
    "TLS_ECDHE_PSK_WITH_3DES_EDE_CBC_SHA" -> 168,
    "TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA" -> 128,
    "TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA" -> 256,
    "TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_PSK_WITH_NULL_SHA" -> 0,
    "TLS_ECDHE_PSK_WITH_NULL_SHA256" -> 0,
    "TLS_ECDHE_PSK_WITH_NULL_SHA384" -> 0,
    "TLS_RSA_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_RSA_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_DH_DSS_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_DH_DSS_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_DH_RSA_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_DH_RSA_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_DHE_DSS_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_DHE_DSS_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_DHE_RSA_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_DHE_RSA_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_DH_anon_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_DH_anon_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_ECDSA_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_ECDSA_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_ECDH_ECDSA_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_ECDH_ECDSA_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_RSA_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_RSA_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_ECDH_RSA_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_ECDH_RSA_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_RSA_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_RSA_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_DHE_RSA_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_DHE_RSA_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_DH_RSA_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_DH_RSA_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_DHE_DSS_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_DHE_DSS_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_DH_DSS_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_DH_DSS_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_DH_anon_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_DH_anon_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_ECDSA_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_ECDHE_ECDSA_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_ECDH_ECDSA_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_ECDH_ECDSA_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_RSA_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_ECDHE_RSA_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_ECDH_RSA_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_ECDH_RSA_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_PSK_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_PSK_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_DHE_PSK_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_DHE_PSK_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_RSA_PSK_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_RSA_PSK_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_PSK_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_PSK_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_DHE_PSK_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_DHE_PSK_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_RSA_PSK_WITH_ARIA_128_GCM_SHA256" -> 128,
    "TLS_RSA_PSK_WITH_ARIA_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_PSK_WITH_ARIA_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_PSK_WITH_ARIA_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_ECDH_ECDSA_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_RSA_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_RSA_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_ECDH_RSA_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_ECDH_RSA_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_RSA_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_RSA_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_DHE_RSA_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_DHE_RSA_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_DH_RSA_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_DH_RSA_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_DHE_DSS_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_DHE_DSS_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_DH_DSS_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_DH_DSS_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_DH_anon_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_DH_anon_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_ECDH_ECDSA_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_ECDH_ECDSA_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_RSA_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_ECDHE_RSA_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_ECDH_RSA_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_ECDH_RSA_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_PSK_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_PSK_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_DHE_PSK_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_DHE_PSK_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_RSA_PSK_WITH_CAMELLIA_128_GCM_SHA256" -> 128,
    "TLS_RSA_PSK_WITH_CAMELLIA_256_GCM_SHA384" -> 256,
    "TLS_PSK_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_PSK_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_DHE_PSK_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_DHE_PSK_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_RSA_PSK_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_RSA_PSK_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_ECDHE_PSK_WITH_CAMELLIA_128_CBC_SHA256" -> 128,
    "TLS_ECDHE_PSK_WITH_CAMELLIA_256_CBC_SHA384" -> 256,
    "TLS_RSA_WITH_AES_128_CCM" -> 128,
    "TLS_RSA_WITH_AES_256_CCM" -> 256,
    "TLS_DHE_RSA_WITH_AES_128_CCM" -> 128,
    "TLS_DHE_RSA_WITH_AES_256_CCM" -> 256,
    "TLS_RSA_WITH_AES_128_CCM_8" -> 128,
    "TLS_RSA_WITH_AES_256_CCM_8" -> 256,
    "TLS_DHE_RSA_WITH_AES_128_CCM_8" -> 128,
    "TLS_DHE_RSA_WITH_AES_256_CCM_8" -> 256,
    "TLS_PSK_WITH_AES_128_CCM" -> 128,
    "TLS_PSK_WITH_AES_256_CCM" -> 256,
    "TLS_DHE_PSK_WITH_AES_128_CCM" -> 128,
    "TLS_DHE_PSK_WITH_AES_256_CCM" -> 256,
    "TLS_PSK_WITH_AES_128_CCM_8" -> 128,
    "TLS_PSK_WITH_AES_256_CCM_8" -> 256,
    "TLS_PSK_DHE_WITH_AES_128_CCM_8" -> 128,
    "TLS_PSK_DHE_WITH_AES_256_CCM_8" -> 256,
    "TLS_ECDHE_ECDSA_WITH_AES_128_CCM" -> 128,
    "TLS_ECDHE_ECDSA_WITH_AES_256_CCM" -> 256,
    "TLS_ECDHE_ECDSA_WITH_AES_128_CCM_8" -> 128,
    "TLS_ECDHE_ECDSA_WITH_AES_256_CCM_8" -> 256,
    "TLS_ECCPWD_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_ECCPWD_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_ECCPWD_WITH_AES_128_CCM_SHA256" -> 128,
    "TLS_ECCPWD_WITH_AES_256_CCM_SHA384" -> 256,
    "TLS_SHA256_SHA256" -> 0,
    "TLS_SHA384_SHA384" -> 0,
    "TLS_GOSTR341112_256_WITH_KUZNYECHIK_CTR_OMAC" -> 256,
    "TLS_GOSTR341112_256_WITH_MAGMA_CTR_OMAC" -> 256,
    "TLS_GOSTR341112_256_WITH_28147_CNT_IMIT" -> 256,
    "TLS_GOSTR341112_256_WITH_KUZNYECHIK_MGM_L" -> 256,
    "TLS_GOSTR341112_256_WITH_MAGMA_MGM_L" -> 256,
    "TLS_GOSTR341112_256_WITH_KUZNYECHIK_MGM_S" -> 256,
    "TLS_GOSTR341112_256_WITH_MAGMA_MGM_S" -> 256,
    "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_PSK_WITH_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_ECDHE_PSK_WITH_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_DHE_PSK_WITH_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_RSA_PSK_WITH_CHACHA20_POLY1305_SHA256" -> 256,
    "TLS_ECDHE_PSK_WITH_AES_128_GCM_SHA256" -> 128,
    "TLS_ECDHE_PSK_WITH_AES_256_GCM_SHA384" -> 256,
    "TLS_ECDHE_PSK_WITH_AES_128_CCM_8_SHA256" -> 128,
    "TLS_ECDHE_PSK_WITH_AES_128_CCM_SHA256" -> 128,
  )

  cipherSuiteMap.foreach { case (name, expectedValue) =>
    test(s"deduceKeyLength should return expected result for $name") {
      assertEquals(tls.deduceKeyLength(name), expectedValue)
    }
  }
}

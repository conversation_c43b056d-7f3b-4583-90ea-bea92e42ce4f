/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s
package headers

import cats.syntax.either._
import org.http4s.laws.discipline.arbitrary._

class DNTSuite extends HeaderLaws {
  checkAll("DNT", headerLaws[DNT])

  test("parsing null into NoPreference") {
    assertEquals(DNT.parser.parseAll("null"), DNT.NoPreference.asRight)
  }

  test("parsing 1 into DisallowTracking") {
    assertEquals(DNT.parser.parseAll("1"), DNT.DisallowTracking.asRight)
  }

  test("parsing 0 into AllowTracking") {
    assertEquals(DNT.parser.parseAll("0"), DNT.AllowTracking.asRight)
  }
}

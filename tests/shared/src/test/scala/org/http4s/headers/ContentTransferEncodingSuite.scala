/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s
package headers

import cats.syntax.either._
import org.http4s.laws.discipline.arbitrary._

class ContentTransferEncodingSuite extends HeaderLaws {
  checkAll("Content-Transfer-Encoding", headerLaws[`Content-Transfer-Encoding`])

  test("parsing case insensitive") {
    assertEquals(
      `Content-Transfer-Encoding`.parser.parseAll("7BIT"),
      `Content-Transfer-Encoding`.`7bit`.asRight,
    )
  }
}

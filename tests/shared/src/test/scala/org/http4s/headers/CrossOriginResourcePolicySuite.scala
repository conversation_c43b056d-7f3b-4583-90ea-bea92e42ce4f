/*
 * Copyright 2013 http4s.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.http4s
package headers

import cats.syntax.either._
import org.http4s.laws.discipline.arbitrary._

class CrossOriginResourcePolicySuite extends HeaderLaws {
  checkAll("Cross-Origin-Resource-Policy", headerLaws[`Cross-Origin-Resource-Policy`])

  test("parsing same-site into SameSite") {
    assertEquals(
      `Cross-Origin-Resource-Policy`.parser.parseAll("same-site"),
      `Cross-Origin-Resource-Policy`.SameSite.asRight,
    )
  }

  test("parsing same-origin into SameOrigin") {
    assertEquals(
      `Cross-Origin-Resource-Policy`.parser.parseAll("same-origin"),
      `Cross-Origin-Resource-Policy`.SameOrigin.asRight,
    )
  }

  test("parsing cross-origin into CrossOrigin") {
    assertEquals(
      `Cross-Origin-Resource-Policy`.parser.parseAll("cross-origin"),
      `Cross-Origin-Resource-Policy`.CrossOrigin.asRight,
    )
  }
}

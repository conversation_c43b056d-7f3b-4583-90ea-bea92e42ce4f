
# Getting Help

## Gitter

The discussion forum for http4s is [http4s/http4s on Gitter][Gitter].
This channel is open to user questions of all levels of experience.
More general questions about Cats, FS2, HTTP, Scala, etc. are always
welcome, but may politely be redirected to a more specific forum.  We
are less concerned about staying strictly on topic than helping
people.  If in doubt, ask.

The contributors also use this channel to discuss development.  Please
do not hesitate to interject with support questions or weigh in on
development.

[blaze] and [rho] do not yet have dedicated channels.  Questions about
these projects are welcome in the http4s channel.

We expect users to observe the [Scala Code of Conduct] while on the Gitter
channel.

[Gitter]: https://gitter.im/http4s/http4s
[blaze]: https://github.com/http4s/blaze
[rho]: https://github.com/http4s/rho

## Issues

http4s uses [GitHub issues].  If you have a bug report or feature
request, please submit an issue.

If we helped you in the Gitter room, and it resulted in a bug, a
feature request, or something to improve in the documentation, we
appreciate it if you open an issue.  Pull requests with documentation
fixes are a great way to start [contributing].

We expect users to observe the [Scala Code of Conduct] on our GitHub
organization.

[GitHub issues]: https://github.com/http4s/http4s/issues
[contributing]: ../contributing/

## Twitter

We tweet from [@http4s].  Twitter is ill-suited to support, but we
encourage you to follow for release announcements.  Your likes and
retweets help grow our community.

[@http4s]: https://twitter.com/http4s
[Scala Code of Conduct]: ../code-of-conduct/


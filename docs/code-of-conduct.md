
# Code of Conduct

## Scala Code of Conduct

We are committed to providing a friendly, safe and welcoming environment for
all, regardless of level of experience, gender, gender identity and expression,
sexual orientation, disability, personal appearance, body size, race, ethnicity,
age, religion, nationality, or other such characteristics.

### Our Standards

**Whether you’re a regular contributor or a newcomer, we care about making this community a welcoming and safe place for you and we’ve got your back.**

As a member of the community, you agree to the following:

**Encouraged:**

- Be kind and courteous.
- Respect differences of opinion and remember that every design or implementation choice carries a trade-off and numerous costs. There is seldom a single right answer.
- Remember that everyone was new to Scala at some point. We want to encourage newcomers to join our community and learn the Scala language and ecosystem. Assume competence.
- Show empathy towards other community members.

**Discouraged:**

- Keep unstructured critique to a minimum. We encourage sharing ideas and perspectives, so please ensure that your feedback is constructive and relevant. If you have solid ideas you want to experiment with, make a fork and see how it works.
- Avoid aggressive and micro-aggressive behavior, such as unconstructive criticism, providing corrections that do not improve the conversation (sometimes referred to as "well actually"s), repeatedly interrupting or talking over someone else, feigning surprise at someone’s lack of knowledge or awareness about a topic, or subtle prejudice (for example, comments like “That’s so easy my grandmother could do it.”). For more examples of this kind of behavior, [see the Recurse Center's user manual](https://www.recurse.com/manual#sec-environment).
- We will exclude you from interaction if you insult, demean or harass anyone. The term “Harassment” includes “Unacceptable Behavior” described in the [Citizen Code of Conduct](http://citizencodeofconduct.org/). **In particular, we don’t tolerate behavior that excludes people in socially marginalized groups.**
- Private harassment is also unacceptable. No matter who you are, if you feel you have been or are being harassed or made uncomfortable by a community member's behavior, please contact one of the [moderators](https://contributors.scala-lang.org/about) or any member of the [Scala Center](http://scala.epfl.ch/) immediately.
- Likewise any spamming, trolling, flaming, baiting or other attention-stealing behaviour is not welcome.

### Moderation

These are the policies for upholding our community’s standards of conduct. If
you feel that a thread needs moderation, please contact anyone on the
moderation team:

- [Christopher Davenport](mailto:<EMAIL>)

- Remarks that violate the above code of conduct, including hateful, hurtful, oppressive, or exclusionary remarks, are not allowed. (Cursing is allowed, but never targeting another user, and never in a hateful manner.)
- Moderators will warn users who make remarks inconsistent with the above code of conduct.
- If the warning is unheeded, the user will be “kicked,” i.e., kicked out of the communication channel to cool off.
- If the user comes back and continues to make trouble, they will be banned, i.e., indefinitely excluded.
- Moderators may choose at their discretion to un-ban the user if it was a first offense and they if they make suitable amends with the offended party.
- If you think a ban is unjustified, please take it up with that moderator, or with a different moderator, in private. Complaints about bans in-channel are not allowed.
- Moderators are held to a higher standard than other community members. If a moderator acts inappropriately, they should expect less leeway than others.

In the Scala community we strive to go the extra step to look out for each
other. Don’t just aim to be technically unimpeachable; try to be your best self.
In particular, avoid exacerbating offensive or sensitive issues, particularly if
they’re off-topic; this all too often leads to unnecessary fights, hurt
feelings, and damaged trust; worse, it can drive people away from the community
entirely.

If someone takes issue with something you said or did, resist the urge to be
defensive. Rather, stop the offending behavior, apologize, and be sensitive
thereafter. Even if you feel you were misinterpreted or unfairly accused,
chances are good there was something you could’ve communicated better — remember
that it’s your responsibility to make your fellow Scala developers comfortable.
We are all here first and foremost because we want to talk about cool
technology, and everyone wants to get along in doing so. People are generally
eager to assume good intent and forgive.

### Domain

The enforcement policies listed above apply to all official http4s channels:

* All [http4s GitHub repositories](https://github.com/http4s)
* The [Gitter channel](https://gitter.im/http4s/http4s)
* Any venues and hackathons organized by or in conjunction with the http4s organization

For other projects adopting the Scala Code of Conduct, please contact
the maintainers of those projects for enforcement. If you wish to use
this code of conduct for your own project, consider explicitly
mentioning your moderation policy or making a copy with your own
moderation policy so as to avoid confusion.

### Credits

Adapted from and/or inspired by multiple successful Codes of Conduct, including:

* [Rust Code of Conduct](https://www.rust-lang.org/en-US/conduct.html)
* [The Node.js Policy on Trolling](http://blog.izs.me/post/30036893703/policy-on-trolling)
* [The Contributor Covenant v1.4.0](http://contributor-covenant.org/version/1/4/)
* [The Recurse Center's User Manual](https://www.recurse.com/manual#sec-environment)
* [The 18F Code of Conduct](https://18f.gsa.gov/code-of-conduct/)

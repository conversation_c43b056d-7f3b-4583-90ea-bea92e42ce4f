
# Adopters

## Industrial

[<PERSON>](https://www.jackhenry.com/)
: Extensively utilizes http4s to deliver mission-critical banking services.

[Cam<PERSON>](http://www.cammy.com)
: Has about 10 services in production at thousands of requests per second.

[ClearScore](https://www.clearscore.com)
: Uses http4s for some API services.

[Eckerd College](https://www.eckerd.edu/)
: Uses http4s for several of its internal services.

[Formation](https://www.formation.ai/)
: Uses the client internally

[HiFi](https://hi.fi/)
: Uses http4s for internal web services

[Hireproof](https://hireproof.io/)
: Uses http4s-server for its JSON API as well as http4s-client to consume external services

[看录取 Kanluqu](https://www.kanluqu.com)
: College application resources for Chinese high school students, built entirely upon the Typelevel stack.

[iHeartRadio](https://www.iheart.com/)
: Uses http4s for internal web services

[MYOB](https://myob.com)
: Uses http4s for some API and Web services.

[Netflix](https://www.netflix.com)
: Uses http4s for internal tools.

[On Air Entertainment](https://onairentertainment.com/)
: Uses http4s for some API services.

[Quantiply](https://www.quantiply.com)
: Uses http4s to power the critical data APIs.

[SecurityScorecard](https://securityscorecard.io)
: Uses http4s to power its critical data pipeline.

[Verizon](http://www.verizon.com)
: Uses http4s extensively in its internal services and [open source projects](http://verizon.github.io).

[Wegtam GmbH](https://www.wegtam.com)
: Uses http4s to implement service and microservice architectures as well as web applications for customers.

[Wide Angle Analytics](https://wideangle.co)
: Wide Angle Analytics uses http4s for a fast and scalable event capture platform. Http4s combined with Scala's type safety results in lean, safe, and efficient service.

[Wolt](https://wolt.com/)
: Uses http4s for some API services.

## Libraries

[Avias](https://github.com/fiadliel/avias)
: Functional AWS API implementation for Scala

[circuit-http4s](https://github.com/ChristopherDavenport/circuit-http4s)
: CircuitBreaker backed Http4s Middlewares

[CouchDB-Scala](https://github.com/beloglazov/couchdb-scala)
: a purely functional Scala client for CouchDB

[Helm](https://github.com/Verizon/helm)
: A native Scala client for interacting with Consul

[http4s-directives](https://github.com/hamnis/http4s-directives)
: Implementation of unfiltered-directives using http4s

[http4s-spnego](https://github.com/novakov-alexey/http4s-spnego)
: http4s middleware for HTTP SPNEGO Authentication (Kerberos)

[http4s-timer](https://github.com/fiadliel/http4s-timer)
: Adds timing capability to http4s, with a possible concrete implementation for New Relic

[http4s-tracer](https://github.com/profunktor/http4s-tracer)
: An end-to-end tracing system for http4s

[kamon-http4s](https://github.com/kamon-io/kamon-http4s)
: Kamon support for http4s

[pureconfig-http4s](https://github.com/pureconfig/pureconfig/tree/master/modules/http4s)
: Adds support for http4s' `Uri` class to PureConfig.

[rho](https://github.com/http4s/rho)
: A self-documenting DSL built on http4s

[scala k8s](https://github.com/hnaderi/scala-k8s)
: Kubernetes client, data models and typesafe manifest generation for scala, scalajs, and scala native; with support for http4s clients on all platforms 

[tsec](https://github.com/jmcardon/tsec)
: A type-safe, functional, general purpose security and cryptography library

[typedapi](https://github.com/pheymann/typedapi)
: Build your API on the type level

## Open Source apps

[0x7e.xyz](https://github.com/timo-schmid/0x7e.xyz)
: A simple link-shortener

[Dashing](https://github.com/benfradet/dashing)
: Dashboards to monitor an open source organization's health

[Docspell](https://github.com/eikek/docspell)
: A personal document (pdf) organizer

[fink](https://github.com/dozed/fink-http4s)
: A simple Scala-based content management system

[fleet-buddy](https://github.com/reactormonk/fleet-buddy)
: Eve Online fleet buddy based on the CREST API

[http4s-demo](http://demo.http4s.org/)
: an [open source](https://github.com/http4s/http4s_demo) demo app built with http4s

[http4sbin](https://github.com/dbousamra/http4sbin)
: A httpbin clone built with http4s

[httpize](http://httpize.herokuapp.com/)
: a httpbin built with http4s [(source)](https://github.com/ppurang/httpize)

[Nelson](https://verizon.github.io/nelson/)
: Automated, multi-region container deployment

[Raster Foundry](https://github.com/raster-foundry/raster-foundry)
: An open source tool for finding, analyzing, and publishing geospatial imagery on the web

[scala-pet-store](https://github.com/pauljamescleary/scala-pet-store)
: An implementation of the java pet store using FP techniques in scala

[scala-steward](https://github.com/fthomas/scala-steward)
: Bot that keeps library dependencies and sbt plugins up-to-date

[Sharry](https://github.com/eikek/sharry)
: A file-sharing web application

[Smart Backpacker App](https://github.com/SmartBackpacker/core)
: Backend of the traveler's app using the Typelevel stack

[todo-backend-typelevel](https://github.com/aeons/todo-backend-typelevel)
: todo-backend implementation using the Typelevel stack

[twitterstorm](https://github.com/ChristopherDavenport/twitterstorm)
: Twitter Streaming API Processing Example Project

[http4s-index](https://github.com/stephennancekivell/http4s-index)
: lists the files and folders becoming a simple light weight web server

[http4s-chatserver](https://martinsnyder.net/projects/chat.html)
: an [open source](https://github.com/MartinSnyder/http4s-chatserver) demonstration of http4s WebSocket support and stateful functional streams

## Giter8 templates

G8 templates provide a fast way to get started with SBT projects by just running `sbt new <template-name>`.

[http4s.g8](https://github.com/http4s/http4s.g8)
: Bootstrap Http4s services

[http4s-app.g8](https://codeberg.org/wegtam/http4s-app.g8)
: Bootstrap web apps based on Http4s including database migrations and on Typelevel Stack

[http4s-tapir.g8](https://codeberg.org/wegtam/http4s-tapir.g8)
: Bootstrap HTTP services using Http4s and sttp tapir (Typed API descRiptions)

[typelevel-stack.g8](https://github.com/gvolpe/typelevel-stack.g8)
: Typelevel Stack (Http4s / Doobie / Circe / Cats Effect / Fs2)


----

@:style(footer)
[@:icon(edit) Add yourself](https://github.com/http4s/http4s/edit/main/website/src/hugo/content/adopters.md), alphabetically, if you please.
@:@

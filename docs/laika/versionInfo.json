{"versions": [{"displayValue": "1", "pathSegment": "v1", "fallbackLink": "/docs/quickstart.html", "label": "<PERSON>"}, {"displayValue": "0.23", "pathSegment": "v0.23", "fallbackLink": "/docs/quickstart.html", "label": "Stable"}, {"displayValue": "0.22", "pathSegment": "v0.22", "fallbackLink": "/docs/quickstart.html", "label": "Stable"}, {"displayValue": "0.21", "pathSegment": "v0.21", "fallbackLink": "/index.html", "label": "EOL"}, {"displayValue": "Help me choose...", "pathSegment": "", "fallbackLink": "/versions.html"}], "linkTargets": [{"path": "/docs/auth.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/client.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/cors.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/csrf.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/deployment.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/dsl.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/entity.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/error-handling.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/gzip.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/hsts.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/integrations.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/json.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/methods.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/middleware.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/quickstart.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/service.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/static.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/streaming.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/testing.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/upgrading.html", "versions": ["v0.22", "v0.23", "v1"]}, {"path": "/docs/uri.html", "versions": ["v0.22", "v0.23", "v1"]}]}
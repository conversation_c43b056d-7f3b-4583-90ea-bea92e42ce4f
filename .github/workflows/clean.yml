# This file was automatically generated by sbt-github-actions using the
# githubWorkflowGenerate task. You should add and commit this file to
# your git repository. It goes without saying that you shouldn't edit
# this file by hand! Instead, if you wish to make changes, you should
# change your sbt build configuration to revise the workflow description
# to meet your needs, then regenerate this file.

name: Clean

on: push

jobs:
  delete-artifacts:
    name: Delete Artifacts
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Delete artifacts
        run: |
          # Customize those three lines with your repository and credentials:
          REPO=${GITHUB_API_URL}/repos/${{ github.repository }}

          # A shortcut to call GitHub API.
          ghapi() { curl --silent --location --user _:$GITHUB_TOKEN "$@"; }

          # A temporary file which receives HTTP response headers.
          TMPFILE=/tmp/tmp.$$

          # An associative array, key: artifact name, value: number of artifacts of that name.
          declare -A ARTCOUNT

          # Process all artifacts on this repository, loop on returned "pages".
          URL=$REPO/actions/artifacts
          while [[ -n "$URL" ]]; do

            # Get current page, get response headers in a temporary file.
            JSON=$(ghapi --dump-header $TMPFILE "$URL")

            # Get URL of next page. Will be empty if we are at the last page.
            URL=$(grep '^Link:' "$TMPFILE" | tr ',' '\n' | grep 'rel="next"' | head -1 | sed -e 's/.*<//' -e 's/>.*//')
            rm -f $TMPFILE

            # Number of artifacts on this page:
            COUNT=$(( $(jq <<<$JSON -r '.artifacts | length') ))

            # Loop on all artifacts on this page.
            for ((i=0; $i < $COUNT; i++)); do

              # Get name of artifact and count instances of this name.
              name=$(jq <<<$JSON -r ".artifacts[$i].name?")
              ARTCOUNT[$name]=$(( $(( ${ARTCOUNT[$name]} )) + 1))

              id=$(jq <<<$JSON -r ".artifacts[$i].id?")
              size=$(( $(jq <<<$JSON -r ".artifacts[$i].size_in_bytes?") ))
              printf "Deleting '%s' #%d, %'d bytes\n" $name ${ARTCOUNT[$name]} $size
              ghapi -X DELETE $REPO/actions/artifacts/$id
            done
          done

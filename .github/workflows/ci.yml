# This file was automatically generated by sbt-github-actions using the
# githubWorkflowGenerate task. You should add and commit this file to
# your git repository. It goes without saying that you shouldn't edit
# this file by hand! Instead, if you wish to make changes, you should
# change your sbt build configuration to revise the workflow description
# to meet your needs, then regenerate this file.

name: Continuous Integration

on:
  pull_request:
    branches: ['**', '!update/**', '!pr/**']
  push:
    branches: ['**', '!update/**', '!pr/**']
    tags: [v*]

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}


concurrency:
  group: ${{ github.workflow }} @ ${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Test
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04]
        scala: [3, 2.12, 2.13]
        java: [temurin@8, temurin@11, temurin@17]
        project: [rootJS, rootJVM, rootNative]
        exclude:
          - scala: 3
            java: temurin@11
          - scala: 3
            java: temurin@17
          - scala: 2.12
            java: temurin@11
          - scala: 2.12
            java: temurin@17
          - project: rootJS
            java: temurin@11
          - project: rootJS
            java: temurin@17
          - project: rootNative
            java: temurin@11
          - project: rootNative
            java: temurin@17
    runs-on: ${{ matrix.os }}
    timeout-minutes: 60
    steps:
      - name: Install Nix
        uses: cachix/install-nix-action@v27

      - name: Checkout current branch (full)
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup sbt
        uses: sbt/setup-sbt@v1

      - name: Setup Java (temurin@8)
        id: setup-java-temurin-8
        if: matrix.java == 'temurin@8'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 8
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@8' && steps.setup-java-temurin-8.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@11)
        id: setup-java-temurin-11
        if: matrix.java == 'temurin@11'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 11
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@11' && steps.setup-java-temurin-11.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@17)
        id: setup-java-temurin-17
        if: matrix.java == 'temurin@17'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@17' && steps.setup-java-temurin-17.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Check that workflows are up to date
        run: 'nix develop .#${{ matrix.java }} -c sbt githubWorkflowCheck'

      - name: Check headers and formatting
        if: matrix.java == 'temurin@8' && matrix.os == 'ubuntu-22.04'
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' headerCheckAll scalafmtCheckAll ''project /'' scalafmtSbtCheck'

      - name: scalaJSLink
        if: matrix.project == 'rootJS'
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' Test/scalaJSLinkerResult'

      - name: nativeLink
        if: matrix.project == 'rootNative'
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' Test/nativeLink'

      - name: Test
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' test'

      - name: Check binary compatibility
        if: matrix.java == 'temurin@8' && matrix.os == 'ubuntu-22.04'
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' mimaReportBinaryIssues'

      - name: Generate API documentation
        if: matrix.java == 'temurin@8' && matrix.os == 'ubuntu-22.04'
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' doc'

      - name: Check scalafix lints
        if: matrix.java == 'temurin@8' && !startsWith(matrix.scala, '3')
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' ''scalafixAll --check'''

      - name: Check unused compile dependencies
        if: matrix.java == 'temurin@8'
        run: 'nix develop .#${{ matrix.java }} -c sbt ''project ${{ matrix.project }}'' ''++ ${{ matrix.scala }}'' unusedCompileDependenciesTest'

  publish:
    name: Publish Artifacts
    needs: [build]
    if: github.event_name != 'pull_request' && (startsWith(github.ref, 'refs/tags/v') || github.ref == 'refs/heads/series/0.23')
    strategy:
      matrix:
        os: [ubuntu-22.04]
        java: [temurin@8]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Install Nix
        uses: cachix/install-nix-action@v27

      - name: Checkout current branch (full)
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup sbt
        uses: sbt/setup-sbt@v1

      - name: Setup Java (temurin@8)
        id: setup-java-temurin-8
        if: matrix.java == 'temurin@8'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 8
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@8' && steps.setup-java-temurin-8.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@11)
        id: setup-java-temurin-11
        if: matrix.java == 'temurin@11'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 11
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@11' && steps.setup-java-temurin-11.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@17)
        id: setup-java-temurin-17
        if: matrix.java == 'temurin@17'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@17' && steps.setup-java-temurin-17.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Import signing key
        if: env.PGP_SECRET != '' && env.PGP_PASSPHRASE == ''
        env:
          PGP_SECRET: ${{ secrets.PGP_SECRET }}
          PGP_PASSPHRASE: ${{ secrets.PGP_PASSPHRASE }}
        run: echo $PGP_SECRET | base64 -d -i - | gpg --import

      - name: Import signing key and strip passphrase
        if: env.PGP_SECRET != '' && env.PGP_PASSPHRASE != ''
        env:
          PGP_SECRET: ${{ secrets.PGP_SECRET }}
          PGP_PASSPHRASE: ${{ secrets.PGP_PASSPHRASE }}
        run: |
          echo "$PGP_SECRET" | base64 -d -i - > /tmp/signing-key.gpg
          echo "$PGP_PASSPHRASE" | gpg --pinentry-mode loopback --passphrase-fd 0 --import /tmp/signing-key.gpg
          (echo "$PGP_PASSPHRASE"; echo; echo) | gpg --command-fd 0 --pinentry-mode loopback --change-passphrase $(gpg --list-secret-keys --with-colons 2> /dev/null | grep '^sec:' | cut --delimiter ':' --fields 5 | tail -n 1)

      - name: Publish
        env:
          SONATYPE_USERNAME: ${{ secrets.SONATYPE_USERNAME }}
          SONATYPE_PASSWORD: ${{ secrets.SONATYPE_PASSWORD }}
          SONATYPE_CREDENTIAL_HOST: ${{ secrets.SONATYPE_CREDENTIAL_HOST }}
        run: 'nix develop .#${{ matrix.java }} -c sbt tlCiRelease'

  dependency-submission:
    name: Submit Dependencies
    if: github.event.repository.fork == false && github.event_name != 'pull_request'
    strategy:
      matrix:
        os: [ubuntu-22.04]
        java: [temurin@8]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Install Nix
        uses: cachix/install-nix-action@v27

      - name: Checkout current branch (full)
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup sbt
        uses: sbt/setup-sbt@v1

      - name: Setup Java (temurin@8)
        id: setup-java-temurin-8
        if: matrix.java == 'temurin@8'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 8
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@8' && steps.setup-java-temurin-8.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@11)
        id: setup-java-temurin-11
        if: matrix.java == 'temurin@11'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 11
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@11' && steps.setup-java-temurin-11.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@17)
        id: setup-java-temurin-17
        if: matrix.java == 'temurin@17'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@17' && steps.setup-java-temurin-17.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Submit Dependencies
        uses: scalacenter/sbt-dependency-submission@v2
        with:
          modules-ignore: http4s-examples-ember_3 http4s-examples-ember_2.12 http4s-examples-ember_2.13 http4s-examples_3 http4s-examples_2.12 http4s-examples_2.13 scalafixinternalinput_3 scalafixinternalinput_2.12 scalafixinternalinput_2.13 scalafixinternaltests_3 scalafixinternaltests_2.12 scalafixinternaltests_2.13 http4s-examples-docker_3 http4s-examples-docker_2.12 http4s-examples-docker_2.13 http4s_3 http4s_2.12 http4s_2.13 http4s-site_3 http4s-site_2.12 http4s-site_2.13 http4s-tests_3 http4s-tests_2.12 http4s-tests_2.13 scalafixinternaloutput_3 scalafixinternaloutput_2.12 scalafixinternaloutput_2.13 http4s-tests_sjs1_3 http4s-tests_sjs1_2.12 http4s-tests_sjs1_2.13 http4s_3 http4s_2.12 http4s_2.13 http4s_3 http4s_2.12 http4s_2.13 sbt-http4s-org-scalafix-internal_3 sbt-http4s-org-scalafix-internal_2.12 sbt-http4s-org-scalafix-internal_2.13 http4s-js-artifact-size-test_sjs1_3 http4s-js-artifact-size-test_sjs1_2.12 http4s-js-artifact-size-test_sjs1_2.13 http4s-tests_native0.4_3 http4s-tests_native0.4_2.12 http4s-tests_native0.4_2.13 http4s-bench_3 http4s-bench_2.12 http4s-bench_2.13
          configs-ignore: test scala-tool scala-doc-tool test-internal

  validate-steward:
    name: Validate Steward Config
    strategy:
      matrix:
        os: [ubuntu-22.04]
        java: [temurin@11]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout current branch (fast)
        uses: actions/checkout@v4

      - name: Setup Java (temurin@11)
        id: setup-java-temurin-11
        if: matrix.java == 'temurin@11'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 11

      - uses: coursier/setup-action@v1
        with:
          apps: scala-steward

      - run: scala-steward validate-repo-config .scala-steward.conf

  coverage:
    name: Generate coverage report
    strategy:
      matrix:
        os: [ubuntu-22.04]
        scala: [2.13.16]
        java: [temurin@8]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Install Nix
        uses: cachix/install-nix-action@v27

      - name: Checkout current branch (full)
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup sbt
        uses: sbt/setup-sbt@v1

      - name: Setup Java (temurin@8)
        id: setup-java-temurin-8
        if: matrix.java == 'temurin@8'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 8
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@8' && steps.setup-java-temurin-8.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@11)
        id: setup-java-temurin-11
        if: matrix.java == 'temurin@11'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 11
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@11' && steps.setup-java-temurin-11.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@17)
        id: setup-java-temurin-17
        if: matrix.java == 'temurin@17'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@17' && steps.setup-java-temurin-17.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - run: 'nix develop .#${{ matrix.java }} -c sbt ''++ ${{ matrix.scala }}'' coverage rootJVM/test coverageAggregate'

      - if: github.event_name != 'pull_request'
        uses: codecov/codecov-action@v3

  site:
    name: Generate Site
    strategy:
      matrix:
        os: [ubuntu-22.04]
        java: [temurin@11]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Install Nix
        uses: cachix/install-nix-action@v27

      - name: Checkout current branch (full)
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup sbt
        uses: sbt/setup-sbt@v1

      - name: Setup Java (temurin@8)
        id: setup-java-temurin-8
        if: matrix.java == 'temurin@8'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 8
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@8' && steps.setup-java-temurin-8.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@11)
        id: setup-java-temurin-11
        if: matrix.java == 'temurin@11'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 11
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@11' && steps.setup-java-temurin-11.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Setup Java (temurin@17)
        id: setup-java-temurin-17
        if: matrix.java == 'temurin@17'
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17
          cache: sbt

      - name: sbt update
        if: matrix.java == 'temurin@17' && steps.setup-java-temurin-17.outputs.cache-hit == 'false'
        run: 'nix develop .#${{ matrix.java }} -c sbt +update'

      - name: Generate site
        run: 'nix develop .#${{ matrix.java }} -c sbt site/tlSite'

      - name: Publish site
        if: github.event_name != 'pull_request' && github.ref == 'refs/heads/series/0.23'
        uses: peaceiris/actions-gh-pages@v4.0.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: site/target/docs/site
          keep_files: true

changelog:
  exclude:
    labels:
      - series/0.21
      - series/0.22
      # - series/0.23
  categories:
    - title: http4s-core
      labels:
        - module:core
      exclude:
        labels:
          - behind-the-scenes
    - title: http4s-dsl
      labels:
        - module:dsl
      exclude:
        labels:
          - behind-the-scenes
    - title: http4s-laws
      labels:
        - module:laws
      exclude:
        labels:
          - behind-the-scenes
    - title: http4s-server
      labels:
        - module:server
      exclude:
        labels:
          - behind-the-scenes
    - title: http4s-client
      labels:
        - module:client
      exclude:
        labels:
          - behind-the-scenes

    - title: http4s-ember-core
      labels:
        - module:ember-core
      exclude:
        labels:
          - behind-the-scenes
    - title: http4s-ember-server
      labels:
        - module:ember-server
      exclude:
        labels:
          - behind-the-scenes
    - title: http4s-ember-client
      labels:
        - module:ember-client
      exclude:
        labels:
          - behind-the-scenes

    - title: http4s-jawn
      labels:
        - module:jawn
      exclude:
        labels:
          - behind-the-scenes

    - title: http4s-circe
      labels:
        - module:circe
      exclude:
        labels:
          - behind-the-scenes

    - title: http4s-client-testkit
      labels:
        - module:client-testkit
      exclude:
        labels:
          - behind-the-scenes

    - title: Scalafixes
      labels:
        - scalafix
      exclude:
        labels:
          - behind-the-scenes

    - title: Documentation
      labels:
        - docs
      exclude:
        labels:
          - behind-the-scenes

    - title: Behind the scenes
      labels:
        - "*"
